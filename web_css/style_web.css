@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
body {
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
    background-color: #1F2937;
    color: #dbdad6;
}

.container {
    margin: 20px;
    text-align: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
}

h2 {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    margin-bottom: 20px;
    background-color: #2b3a52;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

th {
    background-color: #1a2537;
    color: #fdb813;
    text-transform: uppercase;
    font-size: 14px;
    padding: 12px;
    border-bottom: 2px solid #fdb813;
    font-weight: bold;
}

td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
}

tr:hover {
    background-color: #a0a09f;
    color: #06162b;
}

.action-btn,
.add-btn,
.quantity-btn {
    background-color: #3B82F6;
    color: #1F2937;
    border: none;
    padding: 12px 25px;
    cursor: pointer;
    border-radius: 10px;
    font-size: 16px;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.action-btn:hover,
.add-btn:hover,
.quantity-btn:hover {
    background-color: #2563EB;
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:active,
.add-btn:active,
.quantity-btn:active {
    background-color: #1D4ED8;
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    color: #06162b;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    height: 40px;
    /* Increase the height */
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.input-field {
    width: calc(100% - 40px);
    /* Ensure consistent padding from left and right */
    height: 40px;
    /* Set a fixed height for input fields */
    padding: 12px;
    margin: 10px 20px;
    /* Add margin to left and right */
    border-radius: 5px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
    font-size: 16px;
    box-sizing: border-box;
    /* Ensure padding is included in the width and height */
}

.input-field:focus {
    border-color: #6fa3ef;
    box-shadow: 0 0 5px rgba(111, 163, 239, 0.5);
}

.store-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
}

.store-card {
    width: 200px;
    padding: 20px;
    background-color: #343434;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease, background-color 0.3s ease;
    text-align: center;
}

.store-card:hover {
    transform: translateY(-10px) scale(1.05);
    background-color: #fdb813;
    color: #06162b;
}

.store-card h3 {
    margin: 0;
    font-size: 18px;
}

.error-message {
    color: red;
    font-size: 14px;
}

.toggle-btn {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.toggle-btn.inactive {
    background-color: #f44336;
}

.toggle-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
}

.toggle-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.image-preview {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    position: relative;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview .delete-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: red;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.search-field {
    width: 90%;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
}

.search-field:focus {
    border-color: #6fa3ef;
    box-shadow: 0 0 5px rgba(111, 163, 239, 0.5);
}

.checkbox-cell {
    cursor: pointer;
}

.device-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}

.device-status.red {
    background-color: #ff4d4d;
    color: white;
}

.device-status.green {
    background-color: #4caf50;
    color: white;
}

.device-status.orange {
    background-color: #ff9800;
    color: white;
}

.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}

.status.orange {
    background-color: #ff9800;
    color: white;
}

.popup-message {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    font-size: 16px;
    font-weight: bold;
}

.popup-message.success {
    background-color: #4CAF50;
    color: white;
}

.popup-message.error {
    background-color: #f44336;
    color: white;
}

.card-concern {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 1;
    /* Always visible */
    transition: none;
    /* Remove transition */
}

.card-concern .btn-primary {
    background-color: #FFFFFF;
    color: #0A0F1D;
    margin-top: 0;
}

.dark-mode .card-concern .btn-primary {
    background-color: #3B82F6;
    color: #FFFFFF;
}

@media (max-width: 800px) {
    body {
        font-size: 14px;
    }
    h2 {
        font-size: 2em;
    }
    .container {
        margin: 10px;
    }
    .action-btn,
    .add-btn,
    .quantity-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    .input-field,
    .search-field {
        padding: 8px;
    }
    .store-card {
        width: 150px;
        padding: 15px;
    }
    .store-card h3 {
        font-size: 16px;
    }
    .image-preview img {
        width: 80px;
        height: 80px;
    }
}


/* Add styles to make the add-to-cart button smaller and aligned at the bottom. *