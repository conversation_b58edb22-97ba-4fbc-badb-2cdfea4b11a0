<?php
include 'db_connection.php';
include 'auth_check.php';

// معلومات API الواتساب
$api_token = 'EUNG4kO1bBXM5wzsZPfSWDztfWKwMIkhGL37p65voL';
$instance_id = 'instance1681';
$api_base_url = 'https://wapilot.net/api/v1';

// دالة للحصول على حالة الجهاز
function getInstanceStatus($instance_id, $token) {
    global $api_base_url;
    $url = "$api_base_url/instances/$instance_id/status";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . "?token=" . $token);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($httpCode == 200 && !$error) {
        return json_decode($response, true);
    }
    return false;
}

// دالة للحصول على QR Code
function getQRCode($instance_id, $token) {
    global $api_base_url;
    $url = "$api_base_url/instances/$instance_id/qr-code";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url . "?token=" . $token);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($httpCode == 200 && !$error) {
        $decoded = json_decode($response, true);
        if ($decoded && isset($decoded['success']) && $decoded['success']) {
            return $decoded;
        }
    }

    // في حالة الخطأ، إرجاع معلومات مفيدة للتشخيص
    return [
        'success' => false,
        'error' => $error ?: 'HTTP Error: ' . $httpCode,
        'response' => $response
    ];
}

// دالة لتسجيل الخروج
function logoutInstance($instance_id, $token) {
    global $api_base_url;
    $url = "$api_base_url/instances/$instance_id/logout";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['token' => $token]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode == 200;
}

// دالة لإعادة تشغيل الجهاز
function restartInstance($instance_id, $token) {
    global $api_base_url;
    $url = "$api_base_url/instances/$instance_id/restart";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['token' => $token]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode == 200;
}

// دالة لحل المشاكل
function troubleshootInstance($instance_id, $token) {
    global $api_base_url;
    $url = "$api_base_url/instances/$instance_id/troubleshoot";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['token' => $token]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode == 200;
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get_status':
            $status = getInstanceStatus($instance_id, $api_token);
            echo json_encode($status);
            exit;
            
        case 'get_qr':
            $qr = getQRCode($instance_id, $api_token);
            echo json_encode($qr);
            exit;
            
        case 'logout':
            $result = logoutInstance($instance_id, $api_token);
            echo json_encode(['success' => $result]);
            exit;
            
        case 'restart':
            $result = restartInstance($instance_id, $api_token);
            echo json_encode(['success' => $result]);
            exit;
            
        case 'troubleshoot':
            $result = troubleshootInstance($instance_id, $api_token);
            echo json_encode(['success' => $result]);
            exit;
    }
}

// الحصول على الحالة الحالية
$current_status = getInstanceStatus($instance_id, $api_token);
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <link href="uploads\img\logo.png" rel="icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الواتساب</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container">
        <h2>إدارة الواتساب</h2>
        
        <!-- عرض حالة الجهاز -->
        <div class="status-container" style="margin-bottom: 30px;">
            <h3>حالة الجهاز</h3>
            <div id="device-status" class="device-status">
                <?php if ($current_status): ?>
                    <?php 
                    $status_text = '';
                    $status_class = '';
                    
                    if (isset($current_status['session_status'])) {
                        switch ($current_status['session_status']) {
                            case 'CONNECTED':
                                $status_text = 'متصل';
                                $status_class = 'green';
                                break;
                            case 'SCAN_QR_CODE':
                                $status_text = 'يحتاج مسح QR';
                                $status_class = 'orange';
                                break;
                            case 'STARTING':
                                $status_text = 'جاري البدء';
                                $status_class = 'orange';
                                break;
                            default:
                                $status_text = 'غير متصل';
                                $status_class = 'red';
                        }
                    } else {
                        $status_text = 'غير متصل';
                        $status_class = 'red';
                    }
                    ?>
                    <span class="<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                <?php else: ?>
                    <span class="red">خطأ في الاتصال</span>
                <?php endif; ?>
            </div>
        </div>

        <!-- منطقة QR Code -->
        <div id="qr-section" style="<?php echo ($current_status && isset($current_status['session_status']) && $current_status['session_status'] === 'CONNECTED') ? 'display: none;' : ''; ?>">
            <div style="background-color: #2b3a52; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3 style="color: #fdb813; text-align: center;">ربط رقم الواتساب</h3>
                <p style="text-align: center; margin: 15px 0;">امسح الكود التالي بتطبيق الواتساب لربط رقمك:</p>

                <!-- تعليمات الاستخدام -->
                <div style="background-color: #1a2537; padding: 15px; border-radius: 8px; margin: 15px 0; border-right: 4px solid #fdb813;">
                    <h4 style="color: #fdb813; margin-top: 0;">خطوات الربط:</h4>
                    <ol style="color: #dbdad6; line-height: 1.6;">
                        <li>افتح تطبيق الواتساب على هاتفك</li>
                        <li>اذهب إلى الإعدادات ← الأجهزة المرتبطة</li>
                        <li>اضغط على "ربط جهاز"</li>
                        <li>امسح الكود أدناه</li>
                    </ol>
                </div>

                <div id="qr-container" style="text-align: center; margin: 20px 0;">
                    <div id="qr-loading" style="display: none; color: #fdb813;">
                        <p>🔄 جاري تحميل الكود...</p>
                    </div>
                    <div id="qr-image"></div>
                </div>
                <button class="action-btn" onclick="refreshQR()">🔄 تحديث الكود</button>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="control-buttons" style="margin-top: 30px;">
            <div style="background-color: #2b3a52; padding: 20px; border-radius: 10px;">
                <h3 style="color: #fdb813; text-align: center; margin-bottom: 20px;">أدوات التحكم</h3>

                <button class="action-btn" onclick="refreshStatus()" style="margin: 10px;">
                    🔄 تحديث الحالة
                </button>

                <?php if ($current_status && isset($current_status['session_status']) && $current_status['session_status'] === 'CONNECTED'): ?>
                    <div style="background-color: #1a2537; padding: 15px; border-radius: 8px; margin: 15px 0; border-right: 4px solid #4CAF50;">
                        <p style="color: #4CAF50; margin: 0; text-align: center;">✅ الواتساب متصل ويعمل بشكل طبيعي</p>
                    </div>
                    <button class="action-btn" style="background-color: #f44336; margin: 10px;" onclick="logoutWhatsApp()">
                        🚪 تسجيل خروج
                    </button>
                <?php else: ?>
                    <div style="background-color: #1a2537; padding: 15px; border-radius: 8px; margin: 15px 0; border-right: 4px solid #ff9800;">
                        <p style="color: #ff9800; margin: 0; text-align: center;">⚠️ الواتساب غير متصل - يحتاج ربط</p>
                    </div>
                <?php endif; ?>

                <button class="action-btn" style="background-color: #ff9800; margin: 10px;" onclick="restartWhatsApp()">
                    🔄 إعادة تشغيل
                </button>

                <button class="action-btn" style="background-color: #9c27b0; margin: 10px;" onclick="troubleshootWhatsApp()">
                    🔧 حل المشاكل
                </button>

                <!-- معلومات إضافية -->
                <div style="background-color: #1a2537; padding: 15px; border-radius: 8px; margin: 15px 0; border-right: 4px solid #2196F3;">
                    <h4 style="color: #2196F3; margin-top: 0;">معلومات مفيدة:</h4>
                    <ul style="color: #dbdad6; line-height: 1.6; margin: 0;">
                        <li><strong>إعادة التشغيل:</strong> لحل المشاكل البسيطة</li>
                        <li><strong>حل المشاكل:</strong> لإعادة تعيين الجلسة كاملة</li>
                        <li><strong>تسجيل الخروج:</strong> لفصل الرقم من النظام</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث الحالة كل 10 ثوان
        setInterval(refreshStatus, 10000);

        // تحميل QR Code عند تحميل الصفحة
        $(document).ready(function() {
            <?php if ($current_status && isset($current_status['session_status']) && $current_status['session_status'] !== 'CONNECTED'): ?>
                loadQRCode();
            <?php endif; ?>
        });

        function refreshStatus() {
            $.post('whatsapp_manager.php', {action: 'get_status'}, function(response) {
                if (response && response.session_status) {
                    updateStatusDisplay(response.session_status);

                    if (response.session_status === 'CONNECTED') {
                        $('#qr-section').hide();
                        location.reload(); // إعادة تحميل الصفحة لإظهار أزرار التحكم
                    } else if (response.session_status === 'SCAN_QR_CODE') {
                        $('#qr-section').show();
                        loadQRCode();
                    }
                }
            }, 'json').fail(function() {
                console.error('خطأ في تحديث الحالة');
            });
        }

        function updateStatusDisplay(status) {
            let statusText = '';
            let statusClass = '';

            switch (status) {
                case 'CONNECTED':
                    statusText = 'متصل';
                    statusClass = 'green';
                    break;
                case 'SCAN_QR_CODE':
                    statusText = 'يحتاج مسح QR';
                    statusClass = 'orange';
                    break;
                case 'STARTING':
                    statusText = 'جاري البدء';
                    statusClass = 'orange';
                    break;
                default:
                    statusText = 'غير متصل';
                    statusClass = 'red';
            }

            $('#device-status').html('<span class="' + statusClass + '">' + statusText + '</span>');
        }

        function loadQRCode() {
            $('#qr-loading').show();
            $('#qr-image').empty();

            $.post('whatsapp_manager.php', {action: 'get_qr'}, function(response) {
                $('#qr-loading').hide();

                if (response && response.success && response.qr) {
                    // إنشاء صورة من base64
                    const qrImageSrc = 'data:image/png;base64,' + response.qr;
                    $('#qr-image').html('<img src="' + qrImageSrc + '" alt="QR Code" style="max-width: 300px; border: 2px solid #fdb813; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">');
                } else if (response && response.qr_code) {
                    // للتوافق مع التنسيق القديم
                    $('#qr-image').html('<img src="' + response.qr_code + '" alt="QR Code" style="max-width: 300px; border: 2px solid #fdb813; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">');
                } else {
                    $('#qr-image').html('<p style="color: #f44336;">خطأ في تحميل الكود - ' + (response.message || 'غير محدد') + '</p>');
                }
            }, 'json').fail(function() {
                $('#qr-loading').hide();
                $('#qr-image').html('<p style="color: #f44336;">خطأ في الاتصال بالخادم</p>');
            });
        }

        function refreshQR() {
            loadQRCode();
        }

        function logoutWhatsApp() {
            Swal.fire({
                title: 'تأكيد تسجيل الخروج',
                text: 'هل أنت متأكد من تسجيل الخروج من الواتساب؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، تسجيل خروج',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.post('whatsapp_manager.php', {action: 'logout'}, function(response) {
                        if (response.success) {
                            Swal.fire('تم!', 'تم تسجيل الخروج بنجاح', 'success');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            Swal.fire('خطأ!', 'فشل في تسجيل الخروج', 'error');
                        }
                    }, 'json');
                }
            });
        }

        function restartWhatsApp() {
            Swal.fire({
                title: 'تأكيد إعادة التشغيل',
                text: 'هل أنت متأكد من إعادة تشغيل الواتساب؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، إعادة تشغيل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.post('whatsapp_manager.php', {action: 'restart'}, function(response) {
                        if (response.success) {
                            Swal.fire('تم!', 'تم إعادة تشغيل الواتساب بنجاح', 'success');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            Swal.fire('خطأ!', 'فشل في إعادة التشغيل', 'error');
                        }
                    }, 'json');
                }
            });
        }

        function troubleshootWhatsApp() {
            Swal.fire({
                title: 'تأكيد حل المشاكل',
                text: 'سيتم إعادة تعيين الجلسة وإنشاء جلسة جديدة. هل تريد المتابعة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، حل المشاكل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.post('whatsapp_manager.php', {action: 'troubleshoot'}, function(response) {
                        if (response.success) {
                            Swal.fire('تم!', 'تم حل المشاكل بنجاح', 'success');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            Swal.fire('خطأ!', 'فشل في حل المشاكل', 'error');
                        }
                    }, 'json');
                }
            });
        }
    </script>
</body>
</html>
